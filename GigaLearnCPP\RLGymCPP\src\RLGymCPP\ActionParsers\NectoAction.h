#pragma once
#include "ActionParser.h"

namespace RLGC {
    // More advanced discrete action parser, inspired by Necto
    class NectoAction : public ActionParser {
    public:
        std::vector<Action> actions;

        NectoAction();

        virtual Action ParseAction(int index, const Player& player, const GameState& state) override {
            return actions[index];
        }

        virtual int GetActionAmount() override {
            return actions.size();
        }
    };
}