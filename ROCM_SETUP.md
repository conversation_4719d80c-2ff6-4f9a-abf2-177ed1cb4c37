# ROCm Setup Guide for GigaLearnCPP

This guide will help you set up ROCm support for GigaLearnCPP to use your AMD GPU (like the RX 6750 XT) for machine learning acceleration.

## Overview

ROCm (Radeon Open Compute) is AMD's open-source GPU computing platform. GigaLearnCPP now supports ROCm through PyTorch's HIP backend, which provides CUDA-compatible APIs for AMD GPUs.

### GPU Compatibility

Your **AMD RX 6750 XT** uses the **gfx1031** architecture (RDNA2). While not officially supported by AMD in ROCm SDK, it has **runtime support** and can work with some configuration.

**Officially Supported AMD GPUs:**
- RX 6800 XT, RX 6900 XT (gfx1030)
- RX 7000 series (gfx1100+)
- Instinct MI series

**Unofficially Supported (like your RX 6750 XT):**
- RX 6700, RX 6700 XT, RX 6750 XT (gfx1031)
- May require building PyTorch from source

## Installation Options

### Option 1: Windows with WSL2 (Recommended)

ROCm works best on Linux. Using WSL2 provides the most stable experience.

#### Step 1: Install WSL2 with Ubuntu
```powershell
# Run in PowerShell as Administrator
wsl --install -d Ubuntu-22.04
```

#### Step 2: Install ROCm in WSL2
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Add ROCm repository
wget https://repo.radeon.com/amdgpu-install/latest/ubuntu/jammy/amdgpu-install_5.7.50700-1_all.deb
sudo dpkg -i amdgpu-install_5.7.50700-1_all.deb
sudo apt update

# Install ROCm
sudo amdgpu-install --usecase=rocm --no-dkms
```

#### Step 3: Add user to render group
```bash
sudo usermod -a -G render,video $USER
# Logout and login again
```

#### Step 4: Verify ROCm installation
```bash
rocm-smi
# Should show your GPU
```

### Option 2: Native Windows (Experimental)

ROCm 6.2+ has experimental Windows support.

#### Step 1: Install ROCm for Windows
1. Download ROCm installer from [AMD ROCm Windows](https://rocm.docs.amd.com/projects/install-on-windows/en/latest/)
2. Run installer as Administrator
3. Add ROCm to PATH: `C:\Program Files\AMD\ROCm\5.7\bin`

#### Step 2: Set environment variables
```cmd
set ROCM_PATH=C:\Program Files\AMD\ROCm\5.7
set HIP_PATH=%ROCM_PATH%
```

## Building PyTorch with ROCm

### For gfx1031 (RX 6750 XT) Support

Since your GPU isn't officially supported, you'll need to build PyTorch from source:

#### Step 1: Install dependencies
```bash
# Ubuntu/WSL2
sudo apt install python3-dev python3-pip cmake ninja-build

# Install Python dependencies
pip3 install numpy pyyaml mkl mkl-include setuptools cmake cffi typing_extensions future six requests dataclasses
```

#### Step 2: Clone and build PyTorch
```bash
# Clone PyTorch
git clone --recursive https://github.com/pytorch/pytorch
cd pytorch

# Set environment variables for ROCm
export ROCM_PATH=/opt/rocm
export HIP_PATH=$ROCM_PATH
export PYTORCH_ROCM_ARCH="gfx1030;gfx1031"  # Include your architecture

# Configure build
python tools/amd_build/build_amd.py
```

#### Step 3: Install built PyTorch
```bash
pip3 install dist/*.whl
```

### Alternative: Use Pre-built ROCm PyTorch

If available for your system:
```bash
pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/rocm5.7
```

## Building GigaLearnCPP with ROCm

### Step 1: Set environment variables
```bash
export ROCM_PATH=/opt/rocm
export HIP_PATH=$ROCM_PATH
```

### Step 2: Configure CMake
```bash
cd GigaLearnCPP-main
mkdir build && cd build

# Configure with ROCm support
cmake .. -DCMAKE_PREFIX_PATH="$(python3 -c 'import torch;print(torch.utils.cmake_prefix_path)')"
```

### Step 3: Build
```bash
make -j$(nproc)
```

## Configuration

### Using ROCm in your code

```cpp
#include <GigaLearnCPP/Learner.h>

int main() {
    LearnerConfig cfg = {};
    
    // Explicitly use ROCm
    cfg.deviceType = LearnerDeviceType::GPU_ROCM;
    
    // Or auto-detect (will prefer CUDA, then ROCm, then CPU)
    cfg.deviceType = LearnerDeviceType::AUTO;
    
    // Create learner
    GGL::Learner learner(envCreateFn, cfg, stepCallback);
    
    return 0;
}
```

## Troubleshooting

### Common Issues

#### 1. "ROCm is not available to libtorch"
- Verify ROCm installation: `rocm-smi`
- Check environment variables: `echo $ROCM_PATH`
- Ensure PyTorch was built with ROCm support

#### 2. "GPU not detected"
- Your RX 6750 XT (gfx1031) may need custom PyTorch build
- Try setting: `export HSA_OVERRIDE_GFX_VERSION=10.3.0`

#### 3. "Out of memory" errors
- ROCm may use more VRAM than CUDA
- Reduce batch size or model size
- Monitor with: `rocm-smi`

#### 4. Performance issues
- Ensure GPU is in performance mode
- Check thermal throttling
- Use `rocm-smi -a` for detailed monitoring

### Verification

Test ROCm support in Python:
```python
import torch
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"Device count: {torch.cuda.device_count()}")
if torch.cuda.is_available():
    print(f"Device name: {torch.cuda.get_device_name(0)}")
    
# Test tensor operations
x = torch.randn(1000, 1000).cuda()
y = torch.mm(x, x)
print("ROCm tensor operations working!")
```

## Performance Expectations

- **RX 6750 XT**: Expect 60-80% of equivalent NVIDIA GPU performance
- **Memory**: 12GB VRAM should handle most models
- **Compatibility**: Some PyTorch operations may fall back to CPU

## Additional Resources

- [AMD ROCm Documentation](https://rocm.docs.amd.com/)
- [PyTorch ROCm Support](https://pytorch.org/docs/stable/notes/hip.html)
- [ROCm GitHub Issues](https://github.com/ROCm/ROCm/issues)

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify your GPU is detected: `rocm-smi`
3. Test with a simple PyTorch script first
4. Consider using CPU mode as fallback: `cfg.deviceType = LearnerDeviceType::CPU`
