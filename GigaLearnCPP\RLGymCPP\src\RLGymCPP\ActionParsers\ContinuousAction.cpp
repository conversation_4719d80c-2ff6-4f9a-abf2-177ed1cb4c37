#include "ContinuousAction.h"

namespace RLGC {
    ContinuousAction::ContinuousAction() {
        // Initialize with a default action
        currentAction.throttle = 0;
        currentAction.steer = 0;
        currentAction.pitch = 0;
        currentAction.yaw = 0;
        currentAction.roll = 0;
        currentAction.boost = 0;
        currentAction.handbrake = 0;
        currentAction.jump = 0;
    }

    void ContinuousAction::SetAction(const std::vector<float>& actions) {
        if (actions.size() >= 7) {
            currentAction.throttle = actions[0];
            currentAction.steer = actions[1];
            currentAction.pitch = actions[2];
            currentAction.yaw = actions[3];
            currentAction.roll = actions[4];
            currentAction.boost = actions[5] > 0.5;
            currentAction.handbrake = actions[6] > 0.5;
            currentAction.jump = actions.size() > 7 ? actions[7] > 0.5 : false;
        }
    }

    Action ContinuousAction::ParseAction(int index, const Player& player, const GameState& state) {
        // We ignore the index and just return the current action
        return currentAction;
    }

    int ContinuousAction::GetActionAmount() {
        // For a continuous action space, this doesn't really make sense.
        // We'll return 1 as a placeholder.
        return 1;
    }
}