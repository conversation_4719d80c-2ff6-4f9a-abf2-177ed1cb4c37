#pragma once

#include <RLGymCPP/ObsBuilders/ObsBuilder.h>
#include <deque>

namespace RLGC {
    class auraobs : public ObsBuilder {
    public:
        auraobs(int team_size = 3, int history_len = 22, int desired_size = 5000);

        virtual void Reset(const GameState& initialState) override;

        virtual FList BuildObs(const Player& player, const GameState& state) override;

    private:
        static constexpr float POS_COEF = 1 / 5000.f;
        static constexpr float VEL_COEF = 1 / 2300.f;
        static constexpr float ANG_VEL_COEF = 1 / 3.f;

        FList BuildObsForState(const Player& player, const GameState& state);

        void AddPlayerToObs(FList& obs, const Player& player, bool inv, const PhysState& ball);

        int team_size;
        int history_len;
        int desired_size;
        size_t player_feature_size;
        size_t single_frame_size;

        std::deque<GameState> history;
    };
}