#pragma once
#include "ActionParser.h"
#include <vector>

namespace RLGC {
    class ContinuousAction : public ActionParser {
    public:
        Action currentAction;

        ContinuousAction();

        void SetAction(const std::vector<float>& actions);

        virtual Action ParseAction(int index, const Player& player, const GameState& state) override;

        virtual int GetActionAmount() override;
    };
}