#include "NectoAction.h"

// BIG TODO: THIS IS AWFUL, MAKE IT DECENT

RLGC::NectoAction::NectoAction() {
	constexpr float R_T[] = { -1.0f, 0.0f, 1.0f }; // Throttle
	constexpr float R_S[] = { -1.0f, -0.5f, 0.0f, 0.5f, 1.0f }; // Steer, Yaw, Pitch
	constexpr float R_R[] = { -1.0f, 0.0f, 1.0f }; // Roll

	for (float throttle : R_T) {
		for (float steer : R_S) {
			for (float boost : { 0.f, 1.f }) {
				for (float handbrake : { 0.f, 1.f }) {
					if (boost > 0 && throttle != 1) continue; // No throttle when boosting
					actions.push_back({ throttle, steer, 0, steer, 0, 0, boost, handbrake });
				}
			}
		}
	}

	for (float pitch : R_S) {
		for (float yaw : R_S) {
			for (float roll : R_R) {
				for (float jump : { 0.f, 1.f }) {
					for (float boost : { 0.f, 1.f }) {
						if (jump > 0 && yaw != 0) continue; // No yaw when jumping
						actions.push_back({ 1, yaw, pitch, yaw, roll, jump, boost, 0 });
					}
				}
			}
		}
	}
}