#include "AdvancedObs.h"
#include <RLGymCPP/Gamestates/StateUtil.h>
#include <vector>

void RLGC::AdvancedObs::AddPlayerToObs(FList& obs, const Player& player, bool inv, const PhysState& ball) {
	auto phys = InvertPhys(player, inv);

	obs += phys.pos * POS_COEF;
	obs += phys.rotMat.forward;
	obs += phys.rotMat.up;
	obs += phys.vel * VEL_COEF;
	obs += phys.angVel * ANG_VEL_COEF;
	obs += phys.rotMat.Dot(phys.angVel) * ANG_VEL_COEF; // Local ang vel

	// Local ball pos and vel
	obs += phys.rotMat.Dot(ball.pos - phys.pos) * POS_COEF;
	obs += phys.rotMat.Dot(ball.vel - phys.vel) * VEL_COEF;

	obs += player.boost / 100;
	obs += player.isOnGround;
	obs += player.HasFlipOrJump();
	obs += player.isDemoed;
	obs += player.hasJumped; // Allows detecting flip resets
}

RLGC::FList RLGC::AdvancedObs::BuildObs(const Player& player, const GameState& state) {
	FList obs = {};

	bool inv = player.team == Team::ORANGE;

	auto ball = InvertPhys(state.ball, inv);
	auto& pads = state.GetBoostPads(inv);
	auto& padTimers = state.GetBoostPadTimers(inv);

	obs += ball.pos * POS_COEF;
	obs += ball.vel * VEL_COEF;
	obs += ball.angVel * ANG_VEL_COEF;

	for (int i = 0; i < player.prevAction.ELEM_AMOUNT; i++)
		obs += player.prevAction[i];

	for (int i = 0; i < CommonValues::BOOST_LOCATIONS_AMOUNT; i++) {
		// A clever trick that blends the boost pads using their timers
		if (pads[i]) {
			obs += 1.f; // Pad is already available
		} else {
			obs += 1.f / (1.f + padTimers[i]); // Approaches 1 as the pad becomes available
		}
	}

	AddPlayerToObs(obs, player, inv, ball);
	
    // --- START OF NEW PADDING LOGIC ---
    const int MAX_TEAM_SIZE = 3;
    const int PLAYER_FEATURE_SIZE = 29;

    std::vector<const Player*> teammates, opponents;
    for (const auto& otherPlayer : state.players) {
        if (otherPlayer.carId == player.carId) continue;
        (otherPlayer.team == player.team ? teammates : opponents).push_back(&otherPlayer);
    }

    int teammates_added = 0;
    for (const Player* teammate : teammates) {
        if (teammates_added >= MAX_TEAM_SIZE - 1) break;
        AddPlayerToObs(obs, *teammate, inv, ball);
        teammates_added++;
    }
    // Pad remaining teammate slots
    for (int i = 0; i < (MAX_TEAM_SIZE - 1 - teammates_added); ++i) {
        obs.insert(obs.end(), PLAYER_FEATURE_SIZE, 0.f);
    }
    
    int opponents_added = 0;
    for (const Player* opponent : opponents) {
        if (opponents_added >= MAX_TEAM_SIZE) break;
        AddPlayerToObs(obs, *opponent, inv, ball);
        opponents_added++;
    }
    // Pad remaining opponent slots
    for (int i = 0; i < (MAX_TEAM_SIZE - opponents_added); ++i) {
        obs.insert(obs.end(), PLAYER_FEATURE_SIZE, 0.f);
    }
    // --- END OF NEW PADDING LOGIC ---

	return obs;
}