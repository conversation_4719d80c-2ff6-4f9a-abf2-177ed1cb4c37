#pragma once

#include <RLGymCPP/Rewards/Reward.h>
#include <RLGymCPP/CommonValues.h>
#include <RLGymCPP/Gamestates/GameState.h>

class DribbleReward : public RLGC::Reward {
public:
    virtual float GetReward(const RLGC::Player& player, const RLGC::GameState& state, bool isFinal) override {
        const float MIN_BALL_HEIGHT = 109.0f;
        const float MAX_BALL_HEIGHT = 180.0f;
        const float MAX_DISTANCE = 197.0f;
        const float SPEED_MATCH_FACTOR = 2.0f; // Adjust this value to control the importance of speed matching
        const float CAR_MAX_SPEED = RLGC::CommonValues::CAR_MAX_SPEED;

        if (player.isOnGround && state.ball.pos.z >= MIN_BALL_HEIGHT && state.ball.pos.z <= MAX_BALL_HEIGHT && (player.pos - state.ball.pos).Length() < MAX_DISTANCE) {
            float playerSpeed = player.vel.Length();
            float ballSpeed = state.ball.vel.Length();

            // Prevent division by zero if both speeds are zero
            if (playerSpeed + ballSpeed == 0) {
                return 0.0f;
            }

            float speedMatchReward = ((playerSpeed/CAR_MAX_SPEED) + SPEED_MATCH_FACTOR * (1.0f - std::abs(playerSpeed - ballSpeed) / (playerSpeed + ballSpeed))) / 2.0f;
            return speedMatchReward; // Reward for successful dribbling, with a bonus for speed matching, normalized to 1
        } else {
            return 0.0f; // No reward
        }
    }
};
