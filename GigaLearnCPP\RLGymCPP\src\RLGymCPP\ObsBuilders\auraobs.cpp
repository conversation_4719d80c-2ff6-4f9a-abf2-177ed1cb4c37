#include "auraobs.h"
#include <RLGymCPP/Gamestates/StateUtil.h>
#include <algorithm>
#include <random>

namespace RLGC {

    auraobs::auraobs(int team_size, int history_len, int desired_size) :
        team_size(team_size), history_len(history_len), desired_size(desired_size) {
        
        player_feature_size = 29;
        
        single_frame_size =
            9
            + 8
            + 34
            + player_feature_size
            + (team_size - 1) * player_feature_size
            + team_size * player_feature_size;
    }

    void auraobs::Reset(const GameState& initialState) {
        history.clear();
        for (int i = 0; i < history_len; ++i) {
            history.push_back(initialState);
        }
    }

    FList auraobs::BuildObs(const Player& player, const GameState& state) {
        history.push_back(state);
        if (history.size() > history_len) {
            history.pop_front();
        }

        FList full_obs;
        full_obs.reserve(desired_size);

        uint32_t player_car_id = player.carId;

        for (int i = 0; i < history_len; ++i) {
            const GameState& hist_state = history[history_len - 1 - i];

            const Player* hist_player_ptr = nullptr;
            for (const auto& p : hist_state.players) {
                if (p.carId == player_car_id) {
                    hist_player_ptr = &p;
                    break;
                }
            }

            if (hist_player_ptr) {
                FList frame_obs = BuildObsForState(*hist_player_ptr, hist_state);
                full_obs.insert(full_obs.end(), frame_obs.begin(), frame_obs.end());
            } else {
                full_obs.insert(full_obs.end(), single_frame_size, 0.f);
            }
        }

        if (full_obs.size() < desired_size) {
            full_obs.resize(desired_size, 0.f);
        } else if (full_obs.size() > desired_size) {
            full_obs.resize(desired_size);
        }

        return full_obs;
    }

    void auraobs::AddPlayerToObs(FList& obs, const Player& player, bool inv, const PhysState& ball) {
        auto phys = InvertPhys(player, inv);

        obs += phys.pos * POS_COEF;
        obs += phys.rotMat.forward;
        obs += phys.rotMat.up;
        obs += phys.vel * VEL_COEF;
        obs += phys.angVel * ANG_VEL_COEF;
        obs += phys.rotMat.Dot(phys.angVel) * ANG_VEL_COEF;

        obs += phys.rotMat.Dot(ball.pos - phys.pos) * POS_COEF;
        obs += phys.rotMat.Dot(ball.vel - phys.vel) * VEL_COEF;

        obs.push_back(player.boost / 100.f);
        obs.push_back(player.isOnGround);
        obs.push_back(player.HasFlipOrJump());
        obs.push_back(player.isDemoed);
        obs.push_back(player.hasJumped);
    }

    FList auraobs::BuildObsForState(const Player& player, const GameState& state) {
        FList obs;
        obs.reserve(single_frame_size);

        bool inv = player.team == Team::ORANGE;

        auto ball = InvertPhys(state.ball, inv);
        const auto& pads = state.GetBoostPads(inv);
        const auto& pad_timers = state.GetBoostPadTimers(inv);

        obs += ball.pos * POS_COEF;
        obs += ball.vel * VEL_COEF;
        obs += ball.angVel * ANG_VEL_COEF;

        for (float val : player.prevAction) {
            obs.push_back(val);
        }

        for (int i = 0; i < CommonValues::BOOST_LOCATIONS_AMOUNT; i++) {
            obs.push_back(pads[i] ? 1.f : 1.f / (1.f + pad_timers[i]));
        }

        AddPlayerToObs(obs, player, inv, ball);

        std::vector<const Player*> teammates, opponents;
        for (const auto& other : state.players) {
            if (other.carId == player.carId) continue;
            (other.team == player.team ? teammates : opponents).push_back(&other);
        }

        static std::mt19937 rand_engine(std::random_device{}());
        std::shuffle(teammates.begin(), teammates.end(), rand_engine);
        std::shuffle(opponents.begin(), opponents.end(), rand_engine);
        
        int teammates_added = 0;
        for (const Player* teammate : teammates) {
            AddPlayerToObs(obs, *teammate, inv, ball);
            teammates_added++;
        }
        for (int i = 0; i < (team_size - 1 - teammates_added); ++i) {
            obs.insert(obs.end(), player_feature_size, 0.f);
        }
        
        int opponents_added = 0;
        for (const Player* opponent : opponents) {
            AddPlayerToObs(obs, *opponent, inv, ball);
            opponents_added++;
        }
        for (int i = 0; i < (team_size - opponents_added); ++i) {
            obs.insert(obs.end(), player_feature_size, 0.f);
        }

        return obs;
    }
}
